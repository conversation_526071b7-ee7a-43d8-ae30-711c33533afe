'use client'

import { <PERSON><PERSON> } from "@/components/ui/button"
import { cn } from "@/lib/utils"
import { MediaType } from './types'

interface ResultsViewProps {
  output?: string
  error?: string
  plots?: string[]
  language: string
  onSavePlot?: (plotUrl: string, plotIndex: number) => void
}

export function ResultsView({
  output,
  error,
  plots,
  language,
  onSavePlot
}: ResultsViewProps) {
  const handleSavePlot = (mediaData: string, index: number) => {
    if (onSavePlot) {
      onSavePlot(mediaData, index);
    }
  };

  return (
    <div className="max-h-[500px] overflow-y-auto border rounded-md">
      <div className="p-3 space-y-4">
        
        {/* Output/Logs Section - Always show first like <PERSON><PERSON><PERSON> */}
        {(output || error) && (
          <div className="space-y-2">
            <div className="text-xs font-medium text-muted-foreground border-b pb-1">
              Output & Logs
            </div>
            <pre className={cn(
              "whitespace-pre-wrap p-3 rounded-md text-sm font-mono max-h-[200px] overflow-y-auto",
              error ? "bg-red-50 dark:bg-red-950/20 text-red-600 dark:text-red-400 border border-red-200" : "bg-muted"
            )}>
              {output || error}
            </pre>
          </div>
        )}

        {/* Plots/Media Section */}
        {plots && plots.length > 0 && (
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div className="text-xs font-medium text-muted-foreground border-b pb-1 flex-1">
                Visual Output ({plots.length} item{plots.length > 1 ? 's' : ''})
              </div>
              {onSavePlot && (
                <div className="text-xs text-muted-foreground ml-2">
                  Click "Save" to add to dashboard
                </div>
              )}
            </div>

            <div className="grid gap-4">
              {plots.map((plot, index) => {
                // Enhanced media detection and handling for images, videos, and Streamlit apps
                let mediaData = '';
                let mediaType: MediaType = 'image'; // default to image
                let mimeType = '';

                try {
                  // Check if it's already a complete data URL
                  if (plot.startsWith('data:')) {
                    mediaData = plot;
                    // Extract media type from data URL
                    const mimeMatch = plot.match(/data:([^;]+)/);
                    if (mimeMatch) {
                      mimeType = mimeMatch[1];
                      if (mimeType.startsWith('video/')) {
                        mediaType = 'video';
                      } else if (mimeType.startsWith('image/')) {
                        mediaType = 'image';
                      }
                    }
                  }
                  // Check if it's a base64 string that needs a MIME type prefix
                  else if (/^[A-Za-z0-9+/=]+$/.test(plot.substring(0, 20))) {
                    // Detect media type based on the base64 header
                    const isPng = plot.startsWith('iVBOR');
                    const isJpg = plot.startsWith('/9j/');
                    const isGif = plot.startsWith('R0lGOD');
                    
                    if (isPng) {
                      mimeType = 'image/png';
                    } else if (isJpg) {
                      mimeType = 'image/jpeg';
                    } else if (isGif) {
                      mimeType = 'image/gif';
                    } else {
                      mimeType = 'image/png'; // default
                    }
                    
                    mediaData = `data:${mimeType};base64,${plot}`;
                    mediaType = 'image';
                  }
                  // Special case for when the result is a data URL but doesn't have the prefix
                  else if (plot.includes('base64,')) {
                    const base64Part = plot.split('base64,')[1];
                    if (base64Part) {
                      const isPng = base64Part.startsWith('iVBOR');
                      const isJpg = base64Part.startsWith('/9j/');
                      mimeType = isPng ? 'image/png' : (isJpg ? 'image/jpeg' : 'image/png');
                      mediaData = `data:${mimeType};base64,${base64Part}`;
                      mediaType = 'image';
                    } else {
                      mediaData = plot;
                    }
                  }
                  // Check if it's a Streamlit app URL or iframe content
                  else if (plot.includes('streamlit') || plot.includes('localhost:8501') || plot.includes('8501') || plot.includes('<iframe')) {
                    mediaData = plot;
                    mediaType = 'streamlit';
                  }
                  // Check if it's an HTTP/HTTPS URL
                  else if (plot.startsWith('http://') || plot.startsWith('https://')) {
                    mediaData = plot;
                    // Detect media type from URL
                    if (plot.match(/\.(mp4|avi|mov|webm)$/i)) {
                      mediaType = 'video';
                    } else if (plot.match(/\.(jpg|jpeg|png|gif|bmp|webp)$/i) || plot.includes('image')) {
                      mediaType = 'image';
                    } else if (plot.includes('streamlit') || plot.includes('8501')) {
                      mediaType = 'streamlit';
                    } else {
                      mediaType = 'image'; // Default to image for HTTP URLs
                    }
                  }
                  // If it's neither, try to use it as-is (might be a local URL)
                  else {
                    mediaData = plot;
                    mediaType = 'image'; // Default to image
                  }

                  console.log(`Plot ${index + 1} detected as ${mediaType}: ${mediaData.substring(0, 30)}...`);
                } catch (error) {
                  console.error(`Error formatting plot ${index + 1}:`, error);
                  mediaData = '';
                }

                return (
                  <div key={index} className="flex flex-col items-center space-y-2">
                    {mediaData ? (
                      <div className="relative w-full">
                        {/* Render based on media type */}
                        {mediaType === 'video' ? (
                          <video
                            src={mediaData}
                            controls
                            className="w-full h-auto max-h-[400px] rounded-lg shadow-lg"
                            onError={(e) => {
                              console.error('Failed to load video:', { index, dataPreview: mediaData.substring(0, 50) + '...' });
                              e.currentTarget.style.display = 'none';
                            }}
                          >
                            Your browser does not support the video tag.
                          </video>
                        ) : mediaType === 'streamlit' ? (
                          <div className="w-full border rounded-lg overflow-hidden bg-background">
                            {mediaData.includes('<iframe') ? (
                              <div dangerouslySetInnerHTML={{ __html: mediaData }} />
                            ) : (
                              <div className="relative">
                                <iframe
                                  src={mediaData}
                                  width="100%"
                                  height="600"
                                  frameBorder="0"
                                  className="rounded-lg"
                                  title={`Streamlit App ${index + 1}`}
                                  sandbox="allow-scripts allow-same-origin allow-forms allow-popups"
                                  loading="lazy"
                                />
                                <div className="absolute top-2 left-2 bg-black/70 text-white px-2 py-1 rounded text-xs">
                                  Streamlit App: {mediaData}
                                </div>
                              </div>
                            )}
                          </div>
                        ) : (
                          <img
                            src={mediaData}
                            alt={`Plot ${index + 1}`}
                            className="w-full h-auto max-h-[400px] rounded-lg shadow-lg object-contain"
                            onError={(e) => {
                              console.error('Failed to load image:', {
                                index,
                                dataPreview: mediaData.substring(0, 50) + '...'
                              });
                              e.currentTarget.style.display = 'none';
                              const parent = e.currentTarget.parentElement;
                              if (parent) {
                                const errorMsg = document.createElement('div');
                                errorMsg.className = 'text-red-500 text-sm mt-2 p-2 border border-red-200 rounded';
                                errorMsg.textContent = 'Failed to load media. Invalid data format.';
                                parent.appendChild(errorMsg);
                              }
                            }}
                            onLoad={() => {
                              console.log('Image loaded successfully:', index + 1);
                            }}
                          />
                        )}
                        
                        {/* Action buttons overlay */}
                        <div className="absolute top-2 right-2 flex gap-1">
                          {/* Save to Dashboard button */}
                          {onSavePlot && (
                            <Button
                              variant="outline"
                              size="sm"
                              className="bg-background/80 text-xs"
                              onClick={() => handleSavePlot(mediaData, index)}
                            >
                              Save to Dashboard
                            </Button>
                          )}
                          {/* Open in new tab button - only for images and videos */}
                          {mediaType !== 'streamlit' && (
                            <Button
                              variant="outline"
                              size="sm"
                              className="bg-background/80 text-xs"
                              onClick={() => {
                                window.open(mediaData, '_blank');
                              }}
                            >
                              Open in New Tab
                            </Button>
                          )}
                        </div>
                      </div>
                    ) : (
                      <div className="text-red-500 text-sm p-2 border border-red-200 rounded">
                        Invalid media data
                      </div>
                    )}
                    <div className="text-xs text-muted-foreground mt-1">
                      {mediaType === 'video' ? 'Video' : mediaType === 'streamlit' ? 'Streamlit App' : 'Plot'} {index + 1}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        )}

        {/* No results message */}
        {!output && !error && (!plots || plots.length === 0) && (
          <div className="flex flex-col items-center justify-center p-8 text-center">
            <div className="text-muted-foreground mb-4">
              {language === 'python' ?
                "No output yet. Run your Python code to see results here." :
                "No results available. Execute your code to see output."}
            </div>
            <div className="bg-muted p-2 rounded-md text-xs font-mono max-w-xl text-left">
              <p className="mb-2">Example code to generate output:</p>
              <pre className="bg-background p-1 rounded text-xs">
{`# Basic matplotlib plot
import matplotlib.pyplot as plt
import numpy as np

x = np.linspace(0, 10, 100)
y = np.sin(x)
plt.figure(figsize=(10, 6))
plt.plot(x, y)
plt.title('Sine Wave')
result = get_plot()  # Required to display`}
              </pre>

              <p className="mt-4 mb-2">Image URL examples:</p>
              <pre className="bg-background p-1 rounded text-xs">
{`# Display image from URL
image_url = "https://example.com/image.jpg"
result = image_url  # Will display in plot tab

# Or use show_image helper
show_image("https://example.com/photo.png")`}
              </pre>

              <p className="mt-4 mb-2">Streamlit app example:</p>
              <pre className="bg-background p-1 rounded text-xs">
{`# Method 1: Use your deployed Streamlit URL
result = "https://your-app.streamlit.app"

# Method 2: Generate random URL (for demo)
streamlit_url = generate_streamlit_url()
result = streamlit_url`}
              </pre>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
