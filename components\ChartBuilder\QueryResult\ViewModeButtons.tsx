'use client'

import { <PERSON><PERSON> } from "@/components/ui/button"
import { TableIcon, BarChart3, ImageIcon, Maximize2 } from "lucide-react"
import { ViewMode } from './types'

interface ViewModeButtonsProps {
  viewMode: ViewMode
  hasData: boolean
  output?: string
  plots?: string[]
  language: string
  onViewModeChange: (mode: ViewMode) => void
}

export function ViewModeButtons({
  viewMode,
  hasData,
  output,
  plots,
  language,
  onViewModeChange
}: ViewModeButtonsProps) {
  const handleViewModeChange = (mode: ViewMode) => {
    console.log(`Switching to ${mode} view`);
    onViewModeChange(mode);
  };

  return (
    <div className="flex gap-1 px-2 py-1 border-b bg-muted/30">
      {/* Table View button */}
      {hasData && (
        <Button
          variant={viewMode === 'table' ? 'default' : 'outline'}
          size="sm"
          className="h-6 px-1.5 text-[10px]"
          onClick={() => handleViewModeChange('table')}
        >
          <TableIcon className="h-3 w-3 mr-1" />
          Table
        </Button>
      )}

      {/* Chart View button */}
      {hasData && (
        <Button
          variant={viewMode === 'chart' ? 'default' : 'outline'}
          size="sm"
          className="h-6 px-1.5 text-[10px]"
          onClick={() => handleViewModeChange('chart')}
        >
          <BarChart3 className="h-3 w-3 mr-1" />
          Chart
        </Button>
      )}

      {/* Results View button - Combined output and plots like Jupyter */}
      {(output || (plots && plots.length > 0) || (language === 'python')) && (
        <Button
          variant={viewMode === 'results' ? 'default' : 'outline'}
          size="sm"
          className="h-6 px-1.5 text-[10px]"
          onClick={() => {
            // Log results data for debugging
            console.log("Results data:", { output, plots });
            handleViewModeChange('results');
          }}
        >
          <ImageIcon className="h-3 w-3 mr-1" />
          Results {plots?.length ? `(${plots.length})` : ''}
        </Button>
      )}

      {/* GraphicWalker View button */}
      {hasData && (
        <Button
          variant={viewMode === 'graphicwalker' ? 'default' : 'outline'}
          size="sm"
          className="h-6 px-1.5 text-[10px]"
          onClick={() => handleViewModeChange('graphicwalker')}
        >
          <Maximize2 className="h-3 w-3 mr-1" />
          Explore
        </Button>
      )}
    </div>
  );
}
