'use client'

import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Area<PERSON><PERSON> } from "lucide-react"
import { ChartType } from './types'

interface ChartTypeSelectorProps {
  chartType: ChartType
  onChartTypeChange: (type: ChartType) => void
}

export function ChartTypeSelector({
  chartType,
  onChartTypeChange
}: ChartTypeSelectorProps) {
  const chartTypes: { type: ChartType; icon: React.ReactNode; label: string }[] = [
    { type: 'bar', icon: <BarChart3 className="h-3 w-3" />, label: 'Bar' },
    { type: 'line', icon: <LineChart className="h-3 w-3" />, label: 'Line' },
    { type: 'area', icon: <AreaChart className="h-3 w-3" />, label: 'Area' },
    { type: 'pie', icon: <PieChart className="h-3 w-3" />, label: 'Pie' },
  ];

  return (
    <div className="flex gap-1 px-2 py-1 border-b bg-muted/20">
      <span className="text-xs text-muted-foreground self-center mr-2">Chart Type:</span>
      {chartTypes.map(({ type, icon, label }) => (
        <Button
          key={type}
          variant={chartType === type ? 'default' : 'outline'}
          size="sm"
          className="h-6 px-1.5 text-[10px]"
          onClick={() => onChartTypeChange(type)}
        >
          {icon}
          <span className="ml-1">{label}</span>
        </Button>
      ))}
    </div>
  );
}
