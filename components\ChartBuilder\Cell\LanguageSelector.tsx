'use client'

import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Code2, Server, FileText } from "lucide-react"
import { LANGUAGE_OPTIONS } from './types'

interface LanguageSelectorProps {
  value: string
  onChange: (value: string) => void
}

export function LanguageSelector({ value, onChange }: LanguageSelectorProps) {
  const getLanguageIcon = (lang: string) => {
    switch (lang) {
      case 'sql':
        return <Server className="h-3 w-3" />
      case 'python':
        return <Code2 className="h-3 w-3" />
      case 'markdown':
        return <FileText className="h-3 w-3" />
      default:
        return <Code2 className="h-3 w-3" />
    }
  }

  return (
    <Select value={value} onValueChange={onChange}>
      <SelectTrigger className="w-[100px] h-7 text-xs">
        <div className="flex items-center gap-1">
          {getLanguageIcon(value)}
          <SelectValue />
        </div>
      </SelectTrigger>
      <SelectContent>
        {LANGUAGE_OPTIONS.map((option) => (
          <SelectItem key={option.value} value={option.value} className="text-xs">
            <div className="flex items-center gap-2">
              {getLanguageIcon(option.value)}
              {option.label}
            </div>
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  )
}
