'use client'

import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { ChevronDown, ChevronRight, Loader2, CheckCircle, AlertCircle, Database } from "lucide-react"
import { cn } from "@/lib/utils"

interface CellHeaderProps {
  index: number
  language: string
  isLoading?: boolean
  isSuccess?: boolean
  error?: string
  executionTime?: number
  selectedDatasets: Array<{ id: string; name: string; data: any[] }>
  onToggleExpanded?: (cellId: string) => void
  isExpanded?: boolean
  cellId: string
}

export function CellHeader({
  index,
  language,
  isLoading,
  isSuccess,
  error,
  executionTime,
  selectedDatasets,
  onToggleExpanded,
  isExpanded = true,
  cellId
}: CellHeaderProps) {
  const handleToggleExpanded = () => {
    if (onToggleExpanded) {
      onToggleExpanded(cellId)
    }
  }

  return (
    <div className="flex items-center justify-between p-3 border-b bg-muted/30">
      <div className="flex items-center gap-2">
        {/* Expand/Collapse button */}
        {onToggleExpanded && (
          <Button
            variant="ghost"
            size="sm"
            className="h-6 w-6 p-0"
            onClick={handleToggleExpanded}
          >
            {isExpanded ? (
              <ChevronDown className="h-3 w-3" />
            ) : (
              <ChevronRight className="h-3 w-3" />
            )}
          </Button>
        )}

        {/* Cell number */}
        <span className="font-mono text-sm font-medium">
          [{index + 1}]
        </span>

        {/* Language badge */}
        <Badge variant="outline" className="text-xs">
          {language.toUpperCase()}
        </Badge>

        {/* Status indicator */}
        {isLoading && (
          <div className="flex items-center gap-1">
            <Loader2 className="h-3 w-3 animate-spin text-blue-500" />
            <span className="text-xs text-muted-foreground">Running...</span>
          </div>
        )}

        {isSuccess && !isLoading && (
          <div className="flex items-center gap-1">
            <CheckCircle className="h-3 w-3 text-green-500" />
            {executionTime && (
              <span className="text-xs text-muted-foreground">
                {executionTime}ms
              </span>
            )}
          </div>
        )}

        {error && !isLoading && (
          <div className="flex items-center gap-1">
            <AlertCircle className="h-3 w-3 text-red-500" />
            <span className="text-xs text-red-500">Error</span>
          </div>
        )}

        {/* Selected datasets indicator */}
        {selectedDatasets.length > 0 && (
          <div className="flex items-center gap-1">
            <Database className="h-3 w-3 text-blue-500" />
            <span className="text-xs text-muted-foreground">
              {selectedDatasets.length} dataset{selectedDatasets.length > 1 ? 's' : ''}
            </span>
          </div>
        )}
      </div>

      {/* Right side - Dataset names */}
      {selectedDatasets.length > 0 && (
        <div className="flex items-center gap-1">
          <span className="text-xs text-muted-foreground">
            {selectedDatasets.slice(0, 2).map(ds => ds.name).join(', ')}
            {selectedDatasets.length > 2 && ` +${selectedDatasets.length - 2} more`}
          </span>
        </div>
      )}
    </div>
  )
}
