'use client'

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Play, Trash2, Database, MoveUp, MoveDown, FileText, Loader2 } from "lucide-react"
import { LanguageSelector } from './LanguageSelector'

interface CellControlsProps {
  language: string
  onLanguageChange: (language: string) => void
  onExecute: () => void
  onDelete: () => void
  onMoveUp?: () => void
  onMoveDown?: () => void
  showDatasetSelector: boolean
  onToggleDatasetSelector: () => void
  showNotes: boolean
  onToggleNotes: () => void
  isLoading?: boolean
  canMoveUp?: boolean
  canMoveDown?: boolean
}

export function CellControls({
  language,
  onLanguageChange,
  onExecute,
  onDelete,
  onMoveUp,
  onMoveDown,
  showDatasetSelector,
  onToggleDatasetSelector,
  showNotes,
  onToggleNotes,
  isLoading,
  canMoveUp = true,
  canMoveDown = true
}: CellControlsProps) {
  return (
    <div className="flex items-center justify-between px-3 py-2 border-b bg-muted/20">
      {/* Left side - Language and execution */}
      <div className="flex items-center gap-2">
        <LanguageSelector
          value={language}
          onChange={onLanguageChange}
        />

        <Button
          onClick={onExecute}
          disabled={isLoading}
          size="sm"
          className="h-7 px-2"
        >
          {isLoading ? (
            <Loader2 className="h-3 w-3 animate-spin mr-1" />
          ) : (
            <Play className="h-3 w-3 mr-1" />
          )}
          {isLoading ? 'Running' : 'Run'}
        </Button>
      </div>

      {/* Right side - Tools and actions */}
      <div className="flex items-center gap-1">
        {/* Dataset selector toggle */}
        <Button
          variant={showDatasetSelector ? "default" : "outline"}
          size="sm"
          className="h-7 px-2"
          onClick={onToggleDatasetSelector}
        >
          <Database className="h-3 w-3 mr-1" />
          Data
        </Button>

        {/* Notes toggle */}
        <Button
          variant={showNotes ? "default" : "outline"}
          size="sm"
          className="h-7 px-2"
          onClick={onToggleNotes}
        >
          <FileText className="h-3 w-3 mr-1" />
          Notes
        </Button>

        {/* Move up */}
        {onMoveUp && (
          <Button
            variant="outline"
            size="sm"
            className="h-7 w-7 p-0"
            onClick={onMoveUp}
            disabled={!canMoveUp}
          >
            <MoveUp className="h-3 w-3" />
          </Button>
        )}

        {/* Move down */}
        {onMoveDown && (
          <Button
            variant="outline"
            size="sm"
            className="h-7 w-7 p-0"
            onClick={onMoveDown}
            disabled={!canMoveDown}
          >
            <MoveDown className="h-3 w-3" />
          </Button>
        )}

        {/* Delete */}
        <Button
          variant="outline"
          size="sm"
          className="h-7 w-7 p-0 text-red-500 hover:text-red-600 hover:bg-red-50"
          onClick={onDelete}
        >
          <Trash2 className="h-3 w-3" />
        </Button>
      </div>
    </div>
  )
}
