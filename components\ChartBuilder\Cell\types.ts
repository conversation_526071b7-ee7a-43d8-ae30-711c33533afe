// Cell component types
import { ViewMode, ChartType } from '../QueryResult/types'

// Cell data structure
export interface CellData {
  id: string;
  content: string;
  language: string;
  cellType?: 'code' | 'markdown';
  result?: {
    data: any[];
    output?: string;
    plots?: string[];
    variables?: Record<string, any>; // Variables created by this cell
    variableTypes?: Record<string, string>; // Types of variables
    outputType?: OutputTypeInfo; // Information about the output type
  };
  error?: string;
  errorDetails?: {
    message: string;
    code?: string;
    stack?: string;
    serverTrace?: string;
  };
  executionTime?: number;
  isSuccess?: boolean;
  isLoading?: boolean;
  showGraphicWalker?: boolean;
  notes?: string;
  selectedDatasetIds?: string[]; // Selected dataset IDs for each cell
}

// Output type information
export interface OutputTypeInfo {
  type: 'dataframe' | 'plot' | 'image' | 'video' | 'streamlit' | 'text' | 'json' | 'error';
  format?: string;
  shape?: [number, number];
  size?: number;
  variables?: string[];
}

// Cell props interface
export interface CellProps {
  id: string
  content: string
  language: string
  result?: {
    data: any[]
    output?: string
    plots?: string[]
    variables?: Record<string, any>
    variableTypes?: Record<string, string>
    outputType?: OutputTypeInfo
  }
  error?: string
  errorDetails?: {
    message: string;
    code?: string;
    stack?: string;
    serverTrace?: string;
  };
  executionTime?: number
  isSuccess?: boolean
  isLoading?: boolean
  showGraphicWalker?: boolean
  notes?: string
  selectedDatasetIds?: string[]
  
  // Event handlers
  onContentChange: (id: string, content: string) => void
  onLanguageChange: (id: string, language: string) => void
  onExecute: (id: string) => void
  onDelete: (id: string) => void
  onMoveUp?: (id: string) => void
  onMoveDown?: (id: string) => void
  onUpdateNotes?: (id: string, notes: string) => void
  onDatasetSelect?: (id: string, datasetIds: string[]) => void
  
  // Chart and table saving
  onSaveChart?: (data: any[], config: any, chartType: ChartType, chartId?: string) => void
  onSaveTable?: (data: any[], columns: string[], tableId?: string) => void
  onSavePlot?: (plotUrl: string, plotIndex: number, plotId?: string) => void
  
  // View mode
  viewMode?: ViewMode
  onViewModeChange?: (mode: ViewMode) => void
  
  // Available datasets
  availableDatasets?: Array<{
    id: string
    name: string
    data: any[]
  }>
  
  // Cell position
  index?: number
  totalCells?: number
  
  // Expanded state
  isExpanded?: boolean
  onToggleExpanded?: (id: string) => void
}

// Language options
export const LANGUAGE_OPTIONS = [
  { value: 'sql', label: 'SQL' },
  { value: 'python', label: 'Python' },
  { value: 'markdown', label: 'Markdown' }
] as const;

export type LanguageType = typeof LANGUAGE_OPTIONS[number]['value'];

// Monaco editor options
export interface MonacoEditorOptions {
  minimap: { enabled: boolean };
  lineNumbers: 'on' | 'off';
  folding: boolean;
  scrollBeyondLastLine: boolean;
  wordWrap: 'on' | 'off';
  contextmenu: boolean;
  fontSize: number;
  lineHeight: number;
  padding: { top: number; bottom: number };
  suggest?: {
    showKeywords: boolean;
    showSnippets: boolean;
    showFunctions: boolean;
    showVariables: boolean;
  };
  quickSuggestions?: {
    other: boolean;
    comments: boolean;
    strings: boolean;
  };
  parameterHints?: {
    enabled: boolean;
  };
  autoIndent?: 'none' | 'keep' | 'brackets' | 'advanced' | 'full';
  formatOnPaste?: boolean;
  formatOnType?: boolean;
  matchBrackets?: 'never' | 'near' | 'always';
  autoClosingBrackets?: 'never' | 'languageDefined' | 'beforeWhitespace' | 'always';
  autoClosingQuotes?: 'never' | 'languageDefined' | 'beforeWhitespace' | 'always';
  semanticHighlighting?: {
    enabled: boolean;
  };
  occurrencesHighlight?: boolean;
  selectionHighlight?: boolean;
  codeLens?: boolean;
  smoothScrolling?: boolean;
  multiCursorModifier?: 'ctrlCmd' | 'alt';
  wordBasedSuggestions?: boolean;
}
