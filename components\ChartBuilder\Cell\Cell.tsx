'use client'

import { useState, useRef, useEffect, useMemo } from 'react'
import { Card } from "@/components/ui/card"
import Editor from '@monaco-editor/react'
import { useTheme } from 'next-themes'
import { toast } from 'sonner'
import { cn } from "@/lib/utils"

// Import components
import { QueryResult } from '../QueryResult'

import { NotesEditor } from '../NotesEditor'

// Import types
import { CellProps, LANGUAGE_OPTIONS, MonacoEditorOptions } from './types'

// Import sub-components
import { CellHeader } from './CellHeader'
import { CellControls } from './CellControls'


export function Cell({
  id,
  content,
  language,
  result,
  error,
  errorDetails,
  executionTime,
  isSuccess,
  isLoading,
  showGraphicWalker,
  notes = '[]',
  selectedDatasetIds = [],
  onContentChange,
  onLanguageChange,
  onExecute,
  onDelete,
  onMoveUp,
  onMoveDown,
  onUpdateNotes,
  onDatasetSelect,
  onSaveChart,
  onSaveTable,
  onSavePlot,
  viewMode = 'results',
  onViewModeChange,
  availableDatasets = [],
  index = 0,
  totalCells = 1,
  isExpanded = true,
  onToggleExpanded
}: CellProps) {
  const { theme } = useTheme()
  const [isEditorFocused, setIsEditorFocused] = useState(false)
  const [showDatasetSelector, setShowDatasetSelector] = useState(false)
  const [showNotes, setShowNotes] = useState(false)
  const editorRef = useRef<any>(null)

  // Parse notes safely
  const parsedNotes = useMemo(() => {
    try {
      return typeof notes === 'string' ? JSON.parse(notes) : notes || []
    } catch {
      return []
    }
  }, [notes])

  // Get selected datasets
  const selectedDatasets = useMemo(() => {
    return availableDatasets.filter(dataset => 
      selectedDatasetIds.includes(dataset.id)
    )
  }, [availableDatasets, selectedDatasetIds])

  // Monaco editor options with enhanced Python support
  const editorOptions: MonacoEditorOptions = useMemo(() => ({
    minimap: { enabled: false },
    lineNumbers: 'on',
    folding: true,
    scrollBeyondLastLine: false,
    wordWrap: 'on',
    contextmenu: true,
    fontSize: 12,
    lineHeight: 16,
    padding: { top: 4, bottom: 4 },
    // Enhanced Python support
    suggest: {
      showKeywords: true,
      showSnippets: true,
      showFunctions: true,
      showVariables: true,
    },
    quickSuggestions: {
      other: true,
      comments: false,
      strings: false
    },
    parameterHints: {
      enabled: true
    },
    autoIndent: 'full',
    formatOnPaste: true,
    formatOnType: true,
    // Bracket matching and auto-closing
    matchBrackets: 'always',
    autoClosingBrackets: 'always',
    autoClosingQuotes: 'always',
    // Enhanced syntax highlighting
    semanticHighlighting: {
      enabled: true
    },
    // Variable highlighting
    occurrencesHighlight: true,
    selectionHighlight: true,
    // Code lens for better variable tracking
    codeLens: true,
    // Better scrolling
    smoothScrolling: true,
    // Enhanced editing
    multiCursorModifier: 'ctrlCmd',
    wordBasedSuggestions: true,
  }), [])

  // Event handlers
  const handleExecute = () => {
    if (content.trim()) {
      onExecute(id)
    } else {
      toast.error('Please enter some code to execute')
    }
  }

  const handleLanguageChange = (newLanguage: string) => {
    onLanguageChange(id, newLanguage)
    
    // Clear any previous results when changing languages
    // This prevents JSON parsing errors when switching languages
    if (onViewModeChange) {
      onViewModeChange('results'); // Reset to results view (unified like Jupyter)
    }
  }

  const handleDatasetSelect = (datasetIds: string[]) => {
    if (onDatasetSelect) {
      onDatasetSelect(id, datasetIds)
    }
    setShowDatasetSelector(false)
  }

  const handleNotesUpdate = (newNotes: any[]) => {
    if (onUpdateNotes) {
      onUpdateNotes(id, JSON.stringify(newNotes))
    }
  }

  const handleEditorMount = (editor: any) => {
    editorRef.current = editor
    
    // Add keyboard shortcuts
    editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.Enter, () => {
      handleExecute()
    })
  }

  const handleEditorFocus = () => {
    setIsEditorFocused(true)
  }

  const handleEditorBlur = () => {
    setIsEditorFocused(false)
  }

  // Determine if cell has any results to show
  const hasResults = result?.data?.length || result?.output || result?.plots?.length || error

  return (
    <Card className={cn(
      "w-full transition-all duration-200 hover:shadow-md",
      isEditorFocused && "ring-2 ring-blue-500/20",
      error && "border-red-200 dark:border-red-800",
      isSuccess && "border-green-200 dark:border-green-800"
    )}>
      {/* Cell Header */}
      <CellHeader
        index={index}
        language={language}
        isLoading={isLoading}
        isSuccess={isSuccess}
        error={error}
        executionTime={executionTime}
        selectedDatasets={selectedDatasets}
        onToggleExpanded={onToggleExpanded}
        isExpanded={isExpanded}
        cellId={id}
      />

      {/* Cell Content - Only show when expanded */}
      {isExpanded && (
        <div className="space-y-2">
          {/* Controls */}
          <CellControls
            language={language}
            onLanguageChange={handleLanguageChange}
            onExecute={handleExecute}
            onDelete={() => onDelete(id)}
            onMoveUp={onMoveUp ? () => onMoveUp(id) : undefined}
            onMoveDown={onMoveDown ? () => onMoveDown(id) : undefined}
            showDatasetSelector={showDatasetSelector}
            onToggleDatasetSelector={() => setShowDatasetSelector(!showDatasetSelector)}
            showNotes={showNotes}
            onToggleNotes={() => setShowNotes(!showNotes)}
            isLoading={isLoading}
            canMoveUp={index > 0}
            canMoveDown={index < totalCells - 1}
          />

          {/* Dataset Selector */}
          {showDatasetSelector && (
            <div className="px-3 pb-2">
              <DatasetSelector
                availableDatasets={availableDatasets}
                selectedDatasetIds={selectedDatasetIds}
                onDatasetSelect={handleDatasetSelect}
                onClose={() => setShowDatasetSelector(false)}
              />
            </div>
          )}

          {/* Code Editor */}
          <div className="px-3">
            <div className={cn(
              "border rounded-md overflow-hidden transition-colors",
              isEditorFocused && "border-blue-500"
            )}>
              <Editor
                height="120px"
                language={language === 'sql' ? 'sql' : language === 'python' ? 'python' : 'markdown'}
                value={content}
                onChange={(value) => onContentChange(id, value || '')}
                onMount={handleEditorMount}
                theme={theme === 'dark' ? 'vs-dark' : 'light'}
                options={editorOptions}
                loading={<div className="flex items-center justify-center h-[120px]">Loading editor...</div>}
                onFocus={handleEditorFocus}
                onBlur={handleEditorBlur}
              />
            </div>
            
            {/* Keyboard shortcut hint */}
            <div className="text-xs text-muted-foreground mt-1 text-right">
              Press Ctrl+Enter (Cmd+Enter on Mac) to execute
            </div>
          </div>

          {/* Notes Editor */}
          {showNotes && (
            <div className="px-3">
              <NotesEditor
                notes={parsedNotes}
                onNotesChange={handleNotesUpdate}
                onClose={() => setShowNotes(false)}
              />
            </div>
          )}

          {/* Results */}
          {hasResults && (
            <div className="px-3 pb-3">
              <QueryResult
                data={result?.data}
                output={result?.output}
                plots={result?.plots}
                error={error}
                errorDetails={errorDetails}
                isSuccess={isSuccess}
                showGraphicWalker={showGraphicWalker}
                executionTime={executionTime}
                onSaveChart={onSaveChart}
                onSaveTable={onSaveTable}
                onSavePlot={onSavePlot}
                viewMode={viewMode}
                onViewModeChange={onViewModeChange}
                cellId={id}
                language={language}
                onMoveUp={onMoveUp}
                onMoveDown={onMoveDown}
                notes={notes}
                onUpdateNotes={onUpdateNotes}
              />
            </div>
          )}
        </div>
      )}
    </Card>
  )
}
