// QueryResult component types
export interface QueryResultProps {
  data?: any[]
  error?: string
  output?: string
  plots?: string[]
  isSuccess?: boolean
  showGraphicWalker?: boolean
  onSaveChart?: (data: any[], config: any, chartType: 'line' | 'bar' | 'pie' | 'area', chartId?: string) => void
  onSaveTable?: (data: any[], columns: string[], tableId?: string) => void
  onSavePlot?: (plotUrl: string, plotIndex: number, plotId?: string) => void
  errorDetails?: {
    message: string;
    code?: string;
    stack?: string;
    serverTrace?: string;
  };
  executionTime?: number; // in milliseconds
  chartType?: 'line' | 'bar' | 'pie' | 'area'
  onChartTypeChange?: (type: 'line' | 'bar' | 'pie' | 'area') => void
  viewMode?: 'table' | 'chart' | 'results' | 'graphicwalker'
  onViewModeChange?: (mode: 'table' | 'chart' | 'results' | 'graphicwalker') => void
  cellId?: string // Add cellId prop to identify this specific result
  language?: string; // Add language prop to identify the cell language

  // Cell control props
  onMoveUp?: (id: string) => void
  onMoveDown?: (id: string) => void
  notes?: string
  onUpdateNotes?: (id: string, notes: string) => void
}

// View mode type
export type ViewMode = 'table' | 'chart' | 'results' | 'graphicwalker';

// Chart type
export type ChartType = 'line' | 'bar' | 'pie' | 'area';

// Media type for plots
export type MediaType = 'image' | 'video' | 'streamlit' | 'text' | 'json' | 'error';

// Error details interface
export interface ErrorDetails {
  message: string;
  code?: string;
  stack?: string;
  serverTrace?: string;
}

// Chart configuration cache type
export interface ChartConfigCache {
  [key: string]: any;
}
