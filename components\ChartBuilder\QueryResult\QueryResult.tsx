'use client'

import { useState, useMemo, useRef, useEffect } from 'react'
import { But<PERSON> } from "@/components/ui/button"
import { BarChart3, AlertCircle, TableIcon, ImageIcon, LineChart, PieChart, AreaChart, Maximize2, PlusCircle } from "lucide-react"
import { NotesEditor } from '../NotesEditor'
import { ChartVisualizer } from '../ChartVisualizer'
import { Card } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { cn } from "@/lib/utils"
import { GraphicWalkerVisualization } from '../GraphicWalker'
import { toast } from "sonner"
import { RichDataTable } from '../RichDataTable'

// Import types
import { QueryResultProps, ViewMode, ChartType, ChartConfigCache } from './types'

// Import sub-components
import { ViewModeButtons } from './ViewModeButtons'
import { ResultsView } from './ResultsView'
import { ChartTypeSelector } from './ChartTypeSelector'

// Default chart colors array
const COLORS = [
  'hsl(var(--chart-1))',
  'hsl(var(--chart-2))',
  'hsl(var(--chart-3))',
  'hsl(var(--chart-4))',
  'hsl(var(--chart-5))'
];

// Keep track of chart configurations in memory using this simple cache
const chartConfigCache: ChartConfigCache = {};

export function QueryResult({
  data,
  error,
  output,
  plots,
  isSuccess,
  showGraphicWalker = false,
  onSaveChart,
  onSaveTable,
  onSavePlot,
  errorDetails,
  executionTime,
  chartType: propChartType = 'bar',
  onChartTypeChange,
  viewMode: propViewMode = 'results',
  onViewModeChange,
  cellId = 'default',
  language = 'sql',
  // Cell control props
  onMoveUp,
  onMoveDown,
  notes = '[]',
  onUpdateNotes
}: QueryResultProps) {
  // Track rendered state to prevent unnecessary updates
  const hasRenderedChart = useRef(false);

  // Use the props as initial values, but check cache first
  const [viewMode, setViewMode] = useState<ViewMode>(propViewMode);
  const [chartType, setChartType] = useState<ChartType>(() => {
    return chartConfigCache[cellId]?.type || propChartType;
  });

  // Data processing
  const hasData = data && Array.isArray(data) && data.length > 0;
  const processedData = useMemo(() => {
    if (!hasData) return [];
    return data.slice(0, 1000); // Limit for performance
  }, [data, hasData]);

  // Chart configuration
  const chartConfig = useMemo(() => {
    if (!hasData) return null;
    
    const cached = chartConfigCache[cellId];
    if (cached) return cached;

    const columns = Object.keys(processedData[0] || {});
    const numericColumns = columns.filter(col => 
      typeof processedData[0]?.[col] === 'number'
    );

    const defaultConfig = {
      type: chartType,
      xAxis: columns[0] || '',
      yAxis: numericColumns[0] || columns[1] || '',
      color: COLORS[0],
      title: `${chartType.charAt(0).toUpperCase() + chartType.slice(1)} Chart`,
      showGrid: true,
      showLegend: true,
      showLabels: false,
      enableZoom: false,
      multiSeries: false,
      aggregationType: 'none' as const,
      groupBy: '',
      sortBy: '',
      sortOrder: 'asc' as const,
      filterColumn: '',
      filterValue: '',
      limit: 100
    };

    chartConfigCache[cellId] = defaultConfig;
    return defaultConfig;
  }, [hasData, processedData, chartType, cellId]);

  // Event handlers
  const handleViewModeChange = (mode: ViewMode) => {
    if (viewMode !== mode) {
      setViewMode(mode);
      if (onViewModeChange) {
        onViewModeChange(mode);
      }

      // Reset chart rendering flag when switching away from chart
      if (mode !== 'chart') {
        hasRenderedChart.current = false;
      }
    }
  };

  const handleChartTypeChange = (type: ChartType) => {
    setChartType(type);
    if (onChartTypeChange) {
      onChartTypeChange(type);
    }
    
    // Update cache
    if (chartConfigCache[cellId]) {
      chartConfigCache[cellId].type = type;
    }
  };

  const handleChartConfigUpdate = (newConfig: any) => {
    chartConfigCache[cellId] = { ...chartConfigCache[cellId], ...newConfig };
  };

  const handleSaveChart = () => {
    if (onSaveChart && hasData && chartConfig) {
      onSaveChart(processedData, chartConfig, chartType, cellId);
      toast.success('Chart saved to dashboard!');
    }
  };

  const handleSaveTable = () => {
    if (onSaveTable && hasData) {
      const columns = Object.keys(processedData[0] || {});
      onSaveTable(processedData, columns, cellId);
      toast.success('Table saved to dashboard!');
    }
  };

  const handleSavePlot = (plotUrl: string, plotIndex: number) => {
    if (onSavePlot) {
      onSavePlot(plotUrl, plotIndex, `${cellId}-plot-${plotIndex}`);
      toast.success('Plot saved to dashboard!');
    }
  };

  const handleChartButtonClick = () => {
    handleViewModeChange('chart');
  };

  return (
    <Card className="w-full border-0 shadow-none bg-transparent">
      <div className="space-y-0">
        {/* Error Display */}
        {error && (
          <div className="p-2">
            <Alert variant="destructive" className="border-red-200">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription className="text-sm">
                <div className="font-medium mb-1">Execution Error</div>
                <div className="text-xs opacity-90">{error}</div>
                {errorDetails && (
                  <details className="mt-2">
                    <summary className="cursor-pointer text-xs font-medium">Error Details</summary>
                    <pre className="mt-1 text-xs bg-red-50 dark:bg-red-950/20 p-2 rounded overflow-auto max-h-32">
                      {errorDetails.stack || errorDetails.message}
                    </pre>
                  </details>
                )}
              </AlertDescription>
            </Alert>
          </div>
        )}

        {/* View Mode Buttons */}
        <ViewModeButtons
          viewMode={viewMode}
          hasData={hasData}
          output={output}
          plots={plots}
          language={language}
          onViewModeChange={handleViewModeChange}
        />

        {/* Chart Type Selector */}
        {viewMode === 'chart' && (
          <ChartTypeSelector
            chartType={chartType}
            onChartTypeChange={handleChartTypeChange}
          />
        )}

        {/* Content Views */}
        <div className="grid gap-1 px-2 py-1 grid-cols-1">
          {/* Charts Section */}
          {viewMode === 'chart' && hasData && (
            <div className="col-span-full border rounded-md overflow-hidden">
              <div className="h-full">
                <ChartVisualizer
                  key={`chart-${cellId}`}
                  data={processedData}
                  initialChartType={chartType}
                  chartConfig={chartConfig || undefined}
                  showConfig={true}
                  onConfigChange={handleChartConfigUpdate}
                  cellId={cellId}
                />
              </div>
            </div>
          )}

          {/* Table View */}
          {viewMode === 'table' && hasData && (
            <RichDataTable
              data={data || []}
              onSaveTable={onSaveTable}
              onSaveToTable={handleSaveTable}
              maxHeight="350px"
            />
          )}

          {/* GraphicWalker View */}
          {viewMode === 'graphicwalker' && hasData && (
            <div className="col-span-full p-0 border rounded-md">
              <GraphicWalkerVisualization
                data={data || []}
                title="Interactive Data Explorer"
                onBack={() => handleViewModeChange('table')}
              />
            </div>
          )}

          {/* Unified Results View */}
          {viewMode === 'results' && (
            <ResultsView
              output={output}
              error={error}
              plots={plots}
              language={language}
              onSavePlot={handleSavePlot}
            />
          )}
        </div>
      </div>
    </Card>
  );
}
