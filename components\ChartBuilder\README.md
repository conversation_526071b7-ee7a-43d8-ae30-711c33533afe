# ChartBuilder Component Architecture

This document describes the refactored architecture of the ChartBuilder components.

## 📁 Folder Structure

```
components/ChartBuilder/
├── Cell/                          # Cell component and sub-components
│   ├── Cell.tsx                   # Main cell component
│   ├── CellHeader.tsx             # Cell header with status indicators
│   ├── CellControls.tsx           # Cell control buttons
│   ├── LanguageSelector.tsx       # Language selection dropdown
│   ├── types.ts                   # Cell-related types
│   └── index.ts                   # Cell exports
├── QueryResult/                   # QueryResult component and sub-components
│   ├── QueryResult.tsx            # Main query result component
│   ├── ViewModeButtons.tsx        # View mode selection buttons
│   ├── ChartTypeSelector.tsx      # Chart type selection
│   ├── ResultsView.tsx            # Unified results view (Jupyter-like)
│   ├── types.ts                   # QueryResult-related types
│   └── index.ts                   # QueryResult exports
├── chartbuilderlogic/             # Business logic hooks and utilities
├── examples/                      # Example code and templates
└── ChartBuilder.tsx               # Main ChartBuilder component
```

## 🔧 Component Overview

### Cell Component (`Cell/`)

The Cell component represents a single code cell in the notebook interface.

**Main Features:**
- **Enhanced Monaco Editor**: Python syntax highlighting, auto-completion, variable recognition
- **Language Support**: SQL, Python, Markdown with proper syntax highlighting
- **Variable Sharing**: Variables from previous cells are automatically available
- **Real-time Execution**: Immediate feedback and logging
- **Expandable Interface**: Collapsible cells for better organization

**Sub-components:**
- `CellHeader`: Shows cell number, language, execution status, and dataset info
- `CellControls`: Run, delete, move, dataset selection, and notes controls
- `LanguageSelector`: Dropdown for selecting cell language

### QueryResult Component (`QueryResult/`)

The QueryResult component displays execution results in a unified interface.

**Main Features:**
- **Unified Results View**: Combined output and plots like Jupyter notebook
- **Multiple View Modes**: Table, Chart, Results (unified), GraphicWalker
- **Media Support**: Images, videos, Streamlit apps, and data visualizations
- **Real-time Output**: Live display of logs and execution output
- **Save to Dashboard**: Easy saving of charts, tables, and plots

**Sub-components:**
- `ViewModeButtons`: Buttons for switching between different view modes
- `ChartTypeSelector`: Chart type selection for visualization
- `ResultsView`: Unified view showing output, logs, and visual content

## 🎯 Key Features

### 1. Variable Sharing Between Cells
```python
# Cell 1
data = [1, 2, 3, 4, 5]
df = pd.DataFrame({'values': data})

# Cell 2 (automatically has access to 'data' and 'df')
mean_value = df['values'].mean()
result = mean_value
```

### 2. Enhanced Monaco Editor
- **Python Syntax Highlighting**: Full Python language support
- **Auto-completion**: Keywords, functions, and variables
- **Variable Recognition**: Highlights variables from previous cells
- **Code Formatting**: Auto-indent and format on paste/type
- **Keyboard Shortcuts**: Ctrl+Enter (Cmd+Enter) to execute

### 3. Unified Results Display
- **Output First**: Shows logs and print statements first (like Jupyter)
- **Visual Content**: Images, videos, and Streamlit apps below output
- **Real-time Updates**: Live display of execution progress
- **Save Functionality**: Easy saving to dashboard

### 4. Media Support
- **Image URLs**: Direct display of images from URLs
- **Streamlit Apps**: Embedded iframe support
- **Video Support**: HTML5 video player
- **Data Visualizations**: Matplotlib, Plotly, and other chart libraries

## 🔄 Data Flow

1. **Cell Execution**: User writes code and executes cell
2. **Variable Context**: Previous cell variables are passed to backend
3. **Backend Processing**: Code executed with full variable context
4. **Result Processing**: Output, plots, and variables extracted
5. **Display**: Unified results view shows all output types
6. **Variable Persistence**: New variables stored for next cells

## 📝 Type System

### Cell Types
```typescript
interface CellData {
  id: string
  content: string
  language: string
  result?: {
    data: any[]
    output?: string
    plots?: string[]
    variables?: Record<string, any>
    variableTypes?: Record<string, string>
    outputType?: OutputTypeInfo
  }
  // ... other properties
}
```

### QueryResult Types
```typescript
interface QueryResultProps {
  data?: any[]
  output?: string
  plots?: string[]
  viewMode?: ViewMode
  onViewModeChange?: (mode: ViewMode) => void
  // ... other properties
}

type ViewMode = 'table' | 'chart' | 'results' | 'graphicwalker'
```

## 🚀 Usage Examples

### Basic Cell Usage
```tsx
<Cell
  id="cell-1"
  content="print('Hello World')"
  language="python"
  onContentChange={handleContentChange}
  onExecute={handleExecute}
  // ... other props
/>
```

### QueryResult Usage
```tsx
<QueryResult
  data={cellResult.data}
  output={cellResult.output}
  plots={cellResult.plots}
  viewMode="results"
  onViewModeChange={handleViewModeChange}
  // ... other props
/>
```

## 🔧 Customization

### Adding New View Modes
1. Update `ViewMode` type in `QueryResult/types.ts`
2. Add button in `ViewModeButtons.tsx`
3. Add view logic in `QueryResult.tsx`

### Adding New Languages
1. Update `LANGUAGE_OPTIONS` in `Cell/types.ts`
2. Add Monaco language support in `Cell.tsx`
3. Update backend language handling

### Adding New Media Types
1. Update `MediaType` in `QueryResult/types.ts`
2. Add detection logic in `ResultsView.tsx`
3. Add rendering component for new media type

## 🐛 Troubleshooting

### Common Issues
1. **Variable Not Found**: Ensure previous cells executed successfully
2. **Monaco Editor Not Loading**: Check Monaco CDN and imports
3. **Plots Not Displaying**: Verify plot data format and URL accessibility
4. **Type Errors**: Check import paths and type definitions

### Debug Tools
- Console logs show variable context for each cell
- Monaco editor provides syntax error highlighting
- Results view shows detailed error messages
- Browser dev tools for network and rendering issues

## 🔄 Migration Guide

If migrating from the old structure:
1. Update imports to use new component paths
2. Update type imports from new type files
3. Replace old QueryResult usage with new unified interface
4. Update Cell props to use new interface

The refactored architecture provides better maintainability, type safety, and user experience while maintaining backward compatibility.
